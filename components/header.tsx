"use client"

import * as React from "react"
import Link from "next/link"
import Image from "next/image"
import { Menu, X, Stethoscope, Heart, Shield } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { ThemeToggle } from "@/components/theme-toggle"
import { cn } from "@/lib/utils"

// const navigation = [
//   { name: "Products", href: "/products", icon: Stethoscope },
//   { name: "AI Assistant", href: "/ai", icon: Heart },
//   { name: "Company", href: "/company", icon: Shield },
// ]

export function Header() {
  const [isOpen, setIsOpen] = React.useState(false)

  return (
    <header className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-transparent">
      <div className="container mx-auto flex h-16 max-w-screen-2xl items-center justify-between px-4">


        {/* Desktop Navigation */}
        {/* <nav className="hidden md:flex items-center space-x-6 text-sm font-medium">
          {navigation.map((item) => (
            <Link
              key={item.name}
              href={item.href}
              className="flex items-center space-x-2 text-foreground/60 transition-colors hover:text-foreground/80"
            >
              <item.icon className="h-4 w-4" />
              <span>{item.name}</span>
            </Link>
          ))}
        </nav> */}
        
        <div className="flex items-center space-x-2 ml-auto">
          <ThemeToggle />
        </div>
        
        <div className="flex items-center space-x-2">
    
          <div className="absolute left-1/2 transform -translate-x-1/2">
            <Link href="/" className="flex items-center space-x-2">
              <Image src="/OKdarkTsp.png" alt="Our Kidz" width={48} height={48} />
            </Link>
          </div>
          {/* <Button asChild className="hidden md:inline-flex bg-[#14b8a6] text-white">
            <Link href="/contact">
              Contact Us
            </Link>
          </Button> */}

          {/* Mobile Navigation */}
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="md:hidden"
                aria-label="Toggle menu"
              >
                <Menu className="h-5 w-5" />
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[300px] sm:w-[400px]">
              <div className="flex flex-col space-y-4 mt-6">
                <div className="flex items-center space-x-2 pb-4 border-b">
                  <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary">
                    <Heart className="h-5 w-5 text-primary-foreground" />
                  </div>
                  <span className="font-bold">Our Kidz</span>
                </div>
                
                {/* <nav className="flex flex-col space-y-3">
                  {navigation.map((item) => (
                    <Link
                      key={item.name}
                      href={item.href}
                      onClick={() => setIsOpen(false)}
                      className="flex items-center space-x-3 text-lg font-medium text-foreground/60 transition-colors hover:text-foreground/80"
                    >
                      <item.icon className="h-5 w-5" />
                      <span>{item.name}</span>
                    </Link>
                  ))}
                </nav> */}
                
                <div className="pt-4 border-t">
                  <Button asChild className="w-full">
                    <Link href="/contact" onClick={() => setIsOpen(false)}>
                      Get Started
                    </Link>
                  </Button>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  )
}
